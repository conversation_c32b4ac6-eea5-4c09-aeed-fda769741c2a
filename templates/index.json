/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "images_with_text_scrolling_yFGt6Y": {
      "type": "images-with-text-scrolling-hero",
      "blocks": {
        "item_ipJMyQ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/sticks_b2457064-631c-4735-b6b8-c5513a4bac4a.png",
            "mobile_image": "shopify://shop_images/sticks_b2457064-631c-4735-b6b8-c5513a4bac4a.png",
            "star_count": 5,
            "title": "FEEL IT",
            "subheading": "HYDRATION THAT POWERS PERFORMANCE",
            "button_text": "[SHOP ELECTROLYTES]",
            "button_url": "shopify://products/os-electrolyte-stick-packs",
            "button_style": "primary",
            "button_color": "#47de47",
            "button_text_color": "#2a2a2a",
            "content": ""
          }
        }
      },
      "block_order": [
        "item_ipJMyQ"
      ],
      "custom_css": [
        ".images-scrolling-desktop__media-wrapper {z-index: 4;}"
      ],
      "settings": {
        "full_width": true,
        "stack_on_mobile": true,
        "show_counter": false,
        "desktop_image_effect": "reveal",
        "image_position": "end",
        "show_floating_star": false,
        "star_size": 40,
        "star_mobile_top_position": -60,
        "star_mobile_right_position": 60,
        "star_desktop_top_position": 300,
        "star_desktop_right_position": -90,
        "star_opacity": 1,
        "subheading_font_family": "body",
        "subheading_font_size_mobile": 20,
        "subheading_font_size_desktop": 20,
        "subheading_font_weight": "normal",
        "subheading_mobile_max_width": 75,
        "rating_text": "1000+ DIALED IN",
        "background": "",
        "background_gradient": "",
        "text_color": "rgba(0,0,0,0)",
        "heading_color": "",
        "heading_gradient": ""
      }
    },
    "scrolling_text_e3NjJK": {
      "type": "scrolling-text",
      "disabled": true,
      "custom_css": [
        "/* fkDevS */scrolling-text > * {transform: translateX(-60%);}",
        ".scrolling-text__text {padding: 0 3px !important;}",
        " /* fkDevE */"
      ],
      "settings": {
        "full_width": true,
        "text": "0 SUGAR 0 FILLERS 0 ARTIFICIAL SWEETENERS 0 GLUTEN 0 ARTIFICIAL FLAVORS 0 PRESERVATIVES",
        "font_class": "accent",
        "text_size": "xsmall",
        "text_style": "fill",
        "scrolling_mode": "scroll",
        "scrolling_speed": 20,
        "background": "#0d0d0d",
        "background_gradient": "",
        "text_color": "#f0f0f0",
        "text_gradient": "",
        "outline_color": "",
        "outline_width": 1,
        "padding_top": 20,
        "padding_bottom": 20,
        "margin_top": 30,
        "margin_bottom": 30
      }
    },
    "scroll_banner_xxdKei": {
      "type": "scroll-banner",
      "blocks": {
        "shape_9EnipM": {
          "type": "shape",
          "settings": {
            "shape": "star",
            "text": "0",
            "link_url": "",
            "rotate_on_scroll": true,
            "rotate_text_on_scroll": false,
            "shape_color": "#2a2a2a",
            "color": "#e7e8e5",
            "shape_width": 100,
            "shape_width_mobile": 60,
            "text_size": 100,
            "text_size_mobile": 50
          }
        },
        "text_eyHqGL": {
          "type": "text",
          "settings": {
            "text": "<p>SECRETS</p>",
            "link_url": "",
            "external_link": false,
            "text_size": 40,
            "text_size_mobile": 20,
            "color": "#e7e8e5"
          }
        },
        "shape_bVddyC": {
          "type": "shape",
          "settings": {
            "shape": "star",
            "text": "0",
            "link_url": "",
            "rotate_on_scroll": true,
            "rotate_text_on_scroll": false,
            "shape_color": "#2a2a2a",
            "color": "#e7e8e5",
            "shape_width": 100,
            "shape_width_mobile": 60,
            "text_size": 100,
            "text_size_mobile": 50
          }
        },
        "text_eGmwq6": {
          "type": "text",
          "settings": {
            "text": "<p>SUGARS</p>",
            "link_url": "",
            "external_link": false,
            "text_size": 40,
            "text_size_mobile": 20,
            "color": "#e7e8e5"
          }
        },
        "shape_kyYdPN": {
          "type": "shape",
          "settings": {
            "shape": "star",
            "text": "0",
            "link_url": "",
            "rotate_on_scroll": true,
            "rotate_text_on_scroll": false,
            "shape_color": "#2a2a2a",
            "color": "#e7e8e5",
            "shape_width": 100,
            "shape_width_mobile": 60,
            "text_size": 100,
            "text_size_mobile": 50
          }
        },
        "text_mNeJg9": {
          "type": "text",
          "settings": {
            "text": "<p>FILLERS</p>",
            "link_url": "",
            "external_link": false,
            "text_size": 40,
            "text_size_mobile": 20,
            "color": "#e7e8e5"
          }
        }
      },
      "block_order": [
        "shape_9EnipM",
        "text_eyHqGL",
        "shape_bVddyC",
        "text_eGmwq6",
        "shape_kyYdPN",
        "text_mNeJg9"
      ],
      "custom_css": [
        ".path-container {transform: translateX(-700px);}"
      ],
      "settings": {
        "color_bg": "#2a2a2a",
        "color_border": "#2a2a2a",
        "border_width": 0,
        "reverse_direction": true,
        "scroll_speed_desktop": 40,
        "scroll_speed_mobile": 20,
        "padding_top": 30,
        "padding_bottom": 30,
        "margin_top": 20,
        "margin_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "rich_text_Bx69PA": {
      "type": "rich-text",
      "blocks": {
        "heading_LdhkVm": {
          "type": "heading",
          "settings": {
            "text": "Triple Mag Complex",
            "heading_tag": "h2",
            "text_color": "",
            "gradient": ""
          }
        }
      },
      "block_order": [
        "heading_LdhkVm"
      ],
      "custom_css": [],
      "settings": {
        "full_width": true,
        "content_width": "large",
        "text_position": "center",
        "background": "rgba(0,0,0,0)",
        "background_gradient": "",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "impact_text_x7kAdD": {
      "type": "impact-text",
      "blocks": {
        "item_hmLGpK": {
          "type": "item",
          "settings": {
            "title": "71%",
            "subheading": "",
            "content": "<p>OS Electrolyte Pods have <em>300mg of Magnesium,</em> 71% of daily recommended intake - more than any other electrolyte drink on the market.</p>",
            "button_text": "",
            "button_url": "",
            "animate_impact_text": false,
            "animate_impact_text_duration": 2
          }
        },
        "item_6Gtg4X": {
          "type": "item",
          "settings": {
            "title": "80%",
            "subheading": "",
            "content": "<p>Our formula contains the most bioavailable forms of magnesium with <em>up to 80% absorption</em>. </p>",
            "button_text": "",
            "button_url": "",
            "animate_impact_text": false,
            "animate_impact_text_duration": 2
          }
        }
      },
      "block_order": [
        "item_hmLGpK",
        "item_6Gtg4X"
      ],
      "custom_css": [
        ".impact-text--center .impact-text__content {max-width: 400px;}"
      ],
      "settings": {
        "full_width": true,
        "stack_mobile": true,
        "text_alignment": "center",
        "impact_text_style": "fill",
        "text_divider": "none",
        "impact_text_size_ratio": 0.5,
        "background": "rgba(0,0,0,0)",
        "background_gradient": "",
        "heading_text_color": "#0d0d0d",
        "heading_gradient": "",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "",
        "button_text_color": ""
      }
    },
    "impact_text_Xa3XqW": {
      "type": "impact-text",
      "blocks": {
        "item_FgR6CH": {
          "type": "item",
          "settings": {
            "title": "75%",
            "subheading": "",
            "content": "<p>Up to <em>75% of Americans are deficient</em> in Magnesium and 28.2% regularly experience brain fog.</p>",
            "button_text": "",
            "button_url": "",
            "animate_impact_text": false,
            "animate_impact_text_duration": 2
          }
        },
        "item_KQCLEh": {
          "type": "item",
          "settings": {
            "title": "95%",
            "subheading": "",
            "content": "<p>Over <em>95% of your body's enzymes</em> require magnesium as a critical cofactor. Your body needs it for 300+ essential processes. </p>",
            "button_text": "",
            "button_url": "",
            "animate_impact_text": false,
            "animate_impact_text_duration": 2
          }
        }
      },
      "block_order": [
        "item_FgR6CH",
        "item_KQCLEh"
      ],
      "custom_css": [
        ".impact-text--center .impact-text__content {max-width: 400px;}"
      ],
      "settings": {
        "full_width": true,
        "stack_mobile": true,
        "text_alignment": "center",
        "impact_text_style": "fill",
        "text_divider": "none",
        "impact_text_size_ratio": 0.5,
        "background": "rgba(0,0,0,0)",
        "background_gradient": "",
        "heading_text_color": "#0d0d0d",
        "heading_gradient": "",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "",
        "button_text_color": ""
      }
    },
    "ss_comparison_table_12_NhfG6r": {
      "type": "ss-comparison-table-12",
      "blocks": {
        "table_row_w4hQdx": {
          "type": "table_row",
          "settings": {
            "row_heading": "300mg of Magnesium",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_JGPD7n": {
          "type": "table_row",
          "settings": {
            "row_heading": "Reduce Brain Fog",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_pd4bwh": {
          "type": "table_row",
          "settings": {
            "row_heading": "Minimize Bloat",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_PY3VVn": {
          "type": "table_row",
          "settings": {
            "row_heading": "Lower Blood Pressure",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_3ndXYW": {
          "type": "table_row",
          "settings": {
            "row_heading": "Trace Minerals",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_zq9fUB": {
          "type": "table_row",
          "settings": {
            "row_heading": "Fillers",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "none",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_KzM7n7": {
          "type": "table_row",
          "settings": {
            "row_heading": "Sugar",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_TrGQNE": {
          "type": "table_row",
          "settings": {
            "row_heading": "Preservatives",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        }
      },
      "block_order": [
        "table_row_w4hQdx",
        "table_row_JGPD7n",
        "table_row_pd4bwh",
        "table_row_PY3VVn",
        "table_row_3ndXYW",
        "table_row_zq9fUB",
        "table_row_KzM7n7",
        "table_row_TrGQNE"
      ],
      "settings": {
        "content_align": "center",
        "content_align_mobile": "center",
        "heading": "<p>It's Not Even Close</p>",
        "heading_custom": false,
        "heading_font": "josefin_sans_n4",
        "heading_size": 56,
        "heading_size_mobile": 28,
        "heading_height": 130,
        "text": "",
        "text_custom": false,
        "text_font": "josefin_sans_n4",
        "text_size": 18,
        "text_size_mobile": 14,
        "text_height": 150,
        "text_mt": 16,
        "text_mt_mobile": 16,
        "table_columns": 2,
        "table_radius": 20,
        "table_mt": 24,
        "table_mt_mobile": 12,
        "column_width": 250,
        "column_width_mobile": 120,
        "row_heading_width": 260,
        "row_heading_width_mobile": 150,
        "first_heading": "OS",
        "first_heading_image": "shopify://shop_images/icon-logo-star.svg",
        "first_heading_url": "",
        "second_heading": "OTHERS",
        "second_heading_image": "shopify://shop_images/sadface.svg",
        "second_heading_url": "",
        "third_heading": "",
        "third_heading_url": "",
        "four_heading": "",
        "four_heading_url": "",
        "fives_heading": "",
        "fives_heading_url": "",
        "sixth_heading": "",
        "sixth_heading_url": "",
        "heading_image_size": 70,
        "heading_image_size_mobile": 80,
        "heading_image_radius": 0,
        "heading_image_border_thickness": 0,
        "column_heading_custom": false,
        "column_heading_font": "assistant_n4",
        "column_heading_size": 24,
        "column_heading_size_mobile": 14,
        "column_heading_height": 130,
        "column_heading_align": "center",
        "column_heading_align_mobile": "center",
        "column_heading_padding_horizontal": 16,
        "column_heading_padding_horizontal_mobile": 4,
        "column_heading_padding_vertical": 24,
        "column_heading_padding_vertical_mobile": 20,
        "row_padding_horizontal": 16,
        "row_padding_horizontal_mobile": 4,
        "row_padding_vertical": 24,
        "row_padding_vertical_mobile": 20,
        "row_border_thickness": 1,
        "row_heading_custom": false,
        "row_heading_font": "assistant_n4",
        "row_heading_size": 22,
        "row_heading_size_mobile": 18,
        "row_heading_height": 130,
        "value_custom": false,
        "value_font": "assistant_n4",
        "value_size": 24,
        "value_size_mobile": 14,
        "value_height": 130,
        "value_icon_size": 28,
        "value_icon_size_mobile": 24,
        "first_column_icon_size": 28,
        "first_column_icon_size_mobile": 24,
        "second_column_icon_size": 22,
        "second_column_icon_size_mobile": 20,
        "third_column_icon_size": 28,
        "third_column_icon_size_mobile": 24,
        "versus_icon_size": 60,
        "versus_icon_size_mobile": 30,
        "bottom_text": "",
        "bottom_text_custom": false,
        "bottom_text_font": "josefin_sans_n4",
        "bottom_text_size": 12,
        "bottom_text_size_mobile": 12,
        "bottom_text_height": 150,
        "bottom_text_mt": 24,
        "bottom_text_mt_mobile": 20,
        "bottom_text_align": "left",
        "bottom_text_align_mobile": "left",
        "heading_color": "#000000",
        "text_color": "#000000",
        "bottom_text_color": "#afabab",
        "column_heading_color": "#000000",
        "column_heading_active_color": "#2a2a2a",
        "value_color": "#000000",
        "value_active_color": "#2a2a2a",
        "table_column_bg_color": "#e7e8e5",
        "table_column_active_bg_color": "#47de47",
        "heading_image_border_color": "#000000",
        "row_heading_color": "#000000",
        "row_border_color": "#d9d9d9",
        "value_icon_color": "#000000",
        "value_icon_active_color": "#2a2a2a",
        "versus_value_icon_color": "#ffffff",
        "versus_icon_bg_color": "#1a1a1a",
        "versus_vs_bg_color": "#2a2a2a",
        "background_color": "#e7e8e5",
        "background_gradient": "",
        "border_color": "#000000",
        "margin_top": 40,
        "margin_bottom": 76,
        "margin_horizontal": 0,
        "margin_horizontal_mobile": 0,
        "padding_top": 36,
        "padding_bottom": 36,
        "padding_horizontal": 5,
        "padding_horizontal_mobile": 1.5,
        "full_width": false,
        "content_width": 1200,
        "border_thickness": 0,
        "section_radius": 0,
        "lazy": true
      }
    },
    "slider_6tbCGV": {
      "type": "slider",
      "blocks": {
        "testimonial_cqq4fN": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/guy-running-again.png",
            "title": "<p>MAGNESIUM MALATE</p>",
            "text": "<p>ENERGIZING</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_Arze6L": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/zen.jpg",
            "title": "<p>MAGNESIUM GLYCINATE</p>",
            "text": "<p>CALMING</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_Q9R8Ce": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/brain.jpg",
            "title": "<p>MAGNESIUM TAURATE</p>",
            "text": "<p>FOCUSED</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_B7iaKG": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/pink_salt.jpg",
            "title": "<p>HIMALAYAN SALT</p>",
            "text": "<p>BALANCED</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_3n68dz": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/sweat.jpg",
            "title": "<p>POTASSIUM CITRATE</p>",
            "text": "<p>HYDRATING</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_jr7y9P": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/plant.jpg",
            "title": "<p>STEVIA EXTRACT</p>",
            "text": "<p>NATURAL</p>",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_TBLjiK": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/raspberry4.jpg",
            "title": "<p>NATURAL FLAVORS</p>",
            "text": "<p>MOUTHWATERING</p>",
            "name": "",
            "link_url": ""
          }
        }
      },
      "block_order": [
        "testimonial_cqq4fN",
        "testimonial_Arze6L",
        "testimonial_Q9R8Ce",
        "testimonial_B7iaKG",
        "testimonial_3n68dz",
        "testimonial_jr7y9P",
        "testimonial_TBLjiK"
      ],
      "settings": {
        "title": "<p>What's Inside?</p>",
        "subtitle": "",
        "color_text": "#2a2a2a",
        "color_bg": "#e7e8e5",
        "container": "contain",
        "color_bg_block": "rgba(0,0,0,0)",
        "color_text_block": "#2a2a2a",
        "color_border_block": "#2a2a2a",
        "border_width_block": 0,
        "border-media": false,
        "block_padding": true,
        "show_stars": false,
        "shape": "rounded",
        "text_align": "center",
        "media_size": "3/4",
        "s_title_font_desktop": 64,
        "b_title_font_desktop": 22,
        "b_text_font_desktop": 22,
        "b_name_font_desktop": 22,
        "s_title_font_mobile": 26,
        "b_title_font_mobile": 22,
        "b_text_font_mobile": 22,
        "b_name_font_mobile": 22,
        "block_count": 3,
        "navigation": "arrows4",
        "rotate": false,
        "speed-slider": 4,
        "padding-lr": 0,
        "padding-lr-m": 1,
        "slide-space": 0,
        "padding_top": 60,
        "padding_bottom": 60,
        "css_class": "",
        "custom_css": ""
      }
    },
    "ss_steps_5_AYgqCp": {
      "type": "ss-steps-5",
      "blocks": {
        "step_Wb99gn": {
          "type": "step",
          "settings": {
            "question": "MAGNESIUM MALATE",
            "answer": "<p>Magnesium combined with malic acid, a natural compound found in fruits like apples. This unique pairing creates one of the most bioavialable forms of magnesium, with studies showing up to 40% better absorption than magnesium oxide. This form excels at supporting <em>energy production</em> and <em>muscle recovery.</em> </p>",
            "image": "shopify://shop_images/guy-running-again.png"
          }
        },
        "step_ghaf8h": {
          "type": "step",
          "settings": {
            "question": "MAGNESIUM GLYCINATE",
            "answer": "<p>Magnesium combined with glycine, an essential amino acid that acts as a <em>natural calming</em> neurotransmitter. This form has up to 80% absorption and genteleness on the digestive system. The glycine helps transport magnesium directly to cells while providing additional benefits for <em>sleep quality</em> and nervous system functioning.</p>",
            "image": "shopify://shop_images/zen.jpg"
          }
        },
        "step_iJfp9L": {
          "type": "step",
          "settings": {
            "question": "MAGNESIUM TAURATE",
            "answer": "<p>Magnesium combined with taurine, an amino acid that plays a crucial role in neurotransmitter function and brain health. Magnesium taurate crosses the blood-brain barrier, supporting <em>mental clarity</em> and <em>cognitive function</em>. Studies indicate high bioavailabiility, with absorption rates around 65-70%. </p>",
            "image": "shopify://shop_images/brain.jpg"
          }
        },
        "step_VrQ9MX": {
          "type": "step",
          "settings": {
            "question": "PINK HIMALAYAN SALT",
            "answer": "<p>Sources from ancient sea beds in the Himalayan mountains, cystallized over 250 million years ago and protected from modern environmental contaminants. This contains a natural spectrum of <em>over 84 trace minerals</em> including Boron, Selenium, Manganese, Zinc, and Phosphorus.</p>",
            "image": "shopify://shop_images/himalayas.jpg"
          }
        },
        "step_YghnRf": {
          "type": "step",
          "settings": {
            "question": "POTASSIUM CITRATE",
            "answer": "<p>Potassium combined with citric acid, creating a highly bioavailable form that excels at supporting <em>pH balance</em> and <em>hydration</em> at the cellular level. Studies indicate absorption rates of up to 90%, making it one of the most efficient forms for maintaining optimal potassium levels. </p>",
            "image": "shopify://shop_images/sweat.jpg"
          }
        },
        "step_yEmqCJ": {
          "type": "step",
          "settings": {
            "question": "STEVIA EXTRACT",
            "answer": "<p>Unlike artificial sweeteners, stevia is extracted from natural plant compounds called steviol glycosides, which are up to 300 times sweeter than sugar. This <em>zero-calorie natural sweetener</em> has a glycemic index of zero, meaning it won't spike blood sugar levels. Research indicates stevia may actually support healthy glucose metabolism, setting it apart from artificial alternatives.</p>",
            "image": "shopify://shop_images/plant.jpg"
          }
        },
        "step_w3VqPz": {
          "type": "step",
          "settings": {
            "question": "NATURAL FLAVORS",
            "answer": "<p>Derived entirely from plant sources like fruit and botanicals. Unlike artificial flavors created in labs from synthetic chemicals, natural flavors are extracted from real food sources. Each flavor component must meet our strict criteria: <em>no artificial preservatives, no synthetic carriers, and no artificial solvents.</em> We specifically select flavors that use only natural extraction methods and organic compounds to create their taste profiles.</p>",
            "image": "shopify://shop_images/raspberry4.jpg"
          }
        }
      },
      "block_order": [
        "step_Wb99gn",
        "step_ghaf8h",
        "step_iJfp9L",
        "step_VrQ9MX",
        "step_YghnRf",
        "step_yEmqCJ",
        "step_w3VqPz"
      ],
      "disabled": true,
      "settings": {
        "heading": "<h1>What's Inside?</h1>",
        "heading_custom": false,
        "heading_font": "josefin_sans_n4",
        "heading_size": 36,
        "heading_size_mobile": 36,
        "heading_height": 130,
        "sub_heading": "",
        "sub_heading_custom": false,
        "sub_heading_font": "josefin_sans_n4",
        "sub_heading_size": 16,
        "sub_heading_size_mobile": 16,
        "sub_heading_height": 150,
        "sub_heading_mt": 30,
        "sub_heading_mt_mobile": 30,
        "layout_width": 80,
        "heading_align": "center",
        "heading_align_mobile": "center",
        "body_mt": 40,
        "body_mt_mobile": 40,
        "body_gap": 100,
        "active_steps": 1,
        "auto_close": true,
        "number_circle_size": 52,
        "number_circle_size_mobile": 32,
        "number_border_thickness": 3,
        "number_custom": false,
        "number_font": "josefin_sans_n4",
        "number_size": 0,
        "number_size_mobile": 0,
        "number_height": 100,
        "question_custom": false,
        "question_font": "josefin_sans_n4",
        "question_size": 24,
        "question_size_mobile": 24,
        "question_height": 130,
        "answer_custom": false,
        "answer_font": "josefin_sans_n4",
        "answer_size": 18,
        "answer_size_mobile": 18,
        "answer_height": 150,
        "answer_padding_top": 20,
        "answer_padding_bottom": 20,
        "answer_border_thickness": 2,
        "image_width": 50,
        "image_radius": 10,
        "image_ratio": "portrait",
        "image_ratio_mobile": "portrait",
        "image_mt_mobile": 10,
        "number_color": "#2a2a2a",
        "number_active_color": "#ffffff",
        "number_bg_color": "",
        "number_active_bg_color": "#2a2a2a",
        "number_border_color": "#2a2a2a",
        "number_border_active_color": "#2a2a2a",
        "question_color": "#2a2a2a",
        "answer_color": "#2a2a2a",
        "answer_border_color": "#2a2a2a",
        "heading_color": "#2a2a2a",
        "sub_heading_color": "#2a2a2a",
        "background_color": "#e7e8e5",
        "background_gradient": "",
        "border_color": "#2a2a2a",
        "margin_top": 20,
        "margin_bottom": 20,
        "margin_horizontal": 0,
        "margin_horizontal_mobile": 0,
        "padding_top": 20,
        "padding_bottom": 40,
        "padding_horizontal": 3,
        "padding_horizontal_mobile": 1.5,
        "full_width": false,
        "content_width": 1400,
        "border_thickness": 0,
        "section_radius": 0,
        "lazy": false
      }
    },
    "flavor_section_dHQRac": {
      "type": "flavor-section",
      "blocks": {
        "image_eLfdUq": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/fruits3.png",
            "media_width": 30,
            "media_position": "start",
            "text_position": "place-self-center-start text-start",
            "heading_size": 60,
            "heading_size_mobile": 22,
            "icon": "none",
            "icon_width": 48,
            "subheading": "",
            "title": "mouthwatering",
            "content": "<p>real fruit flavors that are downright addicting. taste and feel refreshed. </p>",
            "link_text": "",
            "link_url": "",
            "background": "#e7e8e5",
            "background_gradient": "",
            "text_color": "#2a2a2a",
            "text_gradient": "",
            "button_background": "",
            "button_text_color": "",
            "button_outline_width": 3,
            "button_outline_color": "",
            "button_font_weight": 500,
            "button_font_size": 14,
            "button_spacing": 8,
            "button_1_text": "CITRUS++",
            "button_1_color": "#47de47",
            "button_1_text_color": "#2a2a2a",
            "button_2_text": "FRESH WATERMELON",
            "button_2_color": "#ffb3b3",
            "button_2_text_color": "#2a2a2a",
            "button_3_text": "RASPBERRY LIME",
            "button_3_color": "#ff6c6c",
            "button_3_text_color": "#2a2a2a"
          }
        }
      },
      "block_order": [
        "image_eLfdUq"
      ],
      "disabled": true,
      "settings": {
        "full_width": true,
        "spacing": "none"
      }
    },
    "ss_comparison_table_6_zetXqi": {
      "type": "ss-comparison-table-6",
      "blocks": {
        "table_row_HgVN79": {
          "type": "table_row",
          "settings": {
            "row_heading": "MAGNESIUM",
            "first_column": "300 MG",
            "first_column_icon": "none",
            "second_column": "0 MG",
            "second_column_icon": "none",
            "third_column": "60 MG",
            "third_column_icon": "none",
            "four_column": "25 MG",
            "four_column_icon": "none"
          }
        },
        "table_row_6Vin8n": {
          "type": "table_row",
          "settings": {
            "row_heading": "SODIUM",
            "first_column": "500 MG",
            "first_column_icon": "none",
            "second_column": "560 MG",
            "second_column_icon": "none",
            "third_column": "1000 MG",
            "third_column_icon": "none",
            "four_column": "300 MG",
            "four_column_icon": "none"
          }
        },
        "table_row_4nfCTr": {
          "type": "table_row",
          "settings": {
            "row_heading": "POTASSIUM",
            "first_column": "250 MG",
            "first_column_icon": "none",
            "second_column": "370 MG",
            "second_column_icon": "none",
            "third_column": "200 MG",
            "third_column_icon": "none",
            "four_column": "300 MG",
            "four_column_icon": "none"
          }
        },
        "table_row_AzArG4": {
          "type": "table_row",
          "settings": {
            "row_heading": "SUGAR",
            "first_column": "0 G",
            "first_column_icon": "none",
            "second_column": "11 G",
            "second_column_icon": "none",
            "third_column": "0 G",
            "third_column_icon": "none",
            "four_column": "1 G",
            "four_column_icon": "none"
          }
        },
        "table_row_MgpREP": {
          "type": "table_row",
          "settings": {
            "row_heading": "TRACE MINERALS",
            "first_column": "84+",
            "first_column_icon": "none",
            "second_column": "0",
            "second_column_icon": "none",
            "third_column": "0",
            "third_column_icon": "none",
            "four_column": "0",
            "four_column_icon": "none"
          }
        },
        "table_row_xwEU3d": {
          "type": "table_row",
          "settings": {
            "row_heading": "FILLERS",
            "first_column": "0",
            "first_column_icon": "none",
            "second_column": "SILICON DIOXIDE",
            "second_column_icon": "none",
            "third_column": "0",
            "third_column_icon": "none",
            "four_column": "0",
            "four_column_icon": "none"
          }
        },
        "list_item_nmMUUf": {
          "type": "list_item",
          "settings": {
            "text": "OPTIMAL 2:1 RATIO"
          }
        },
        "list_item_WeqUpb": {
          "type": "list_item",
          "settings": {
            "text": "MAGNESIUM POWERED"
          }
        },
        "list_item_yBACrB": {
          "type": "list_item",
          "settings": {
            "text": "REDUCES BLOAT"
          }
        },
        "list_item_n7YHwr": {
          "type": "list_item",
          "settings": {
            "text": "NO JITTERS"
          }
        }
      },
      "block_order": [
        "table_row_HgVN79",
        "table_row_6Vin8n",
        "table_row_4nfCTr",
        "table_row_AzArG4",
        "table_row_MgpREP",
        "table_row_xwEU3d",
        "list_item_nmMUUf",
        "list_item_WeqUpb",
        "list_item_yBACrB",
        "list_item_n7YHwr"
      ],
      "disabled": true,
      "settings": {
        "body_gap": 0,
        "body_gap_mobile": 32,
        "content_horizontal_align": "start",
        "content_horizontal_align_mobile": "start",
        "heading": "<h2>OS vs. Them</h2>",
        "heading_custom": false,
        "heading_font": "josefin_sans_n4",
        "heading_size": 30,
        "heading_size_mobile": 28,
        "heading_height": 150,
        "text": "",
        "text_custom": true,
        "text_font": "josefin_sans_n4",
        "text_size": 10,
        "text_size_mobile": 5,
        "text_height": 120,
        "text_mt": 4,
        "list_mt": 50,
        "list_mt_mobile": 32,
        "list_gap": 30,
        "list_gap_mobile": 30,
        "list_hide_mobile": true,
        "icon_size": 28,
        "icon_size_mobile": 28,
        "icon_margin_right": 16,
        "list_text_custom": false,
        "list_text_font": "josefin_sans_n4",
        "list_text_size": 12,
        "list_text_size_mobile": 18,
        "list_text_height": 150,
        "button": "FEEL IT",
        "button_url": "",
        "button_custom": false,
        "button_font": "josefin_sans_n4",
        "button_size": 18,
        "button_size_mobile": 18,
        "button_height": 100,
        "button_width": 305,
        "button_mt": 50,
        "button_mt_mobile": 32,
        "button_padding_vertical": 16,
        "button_padding_vertical_mobile": 16,
        "button_padding_horizontal": 48,
        "button_padding_horizontal_mobile": 48,
        "button_radius": 10,
        "button_border_thickness": 0,
        "button_style": "non_outline",
        "button_position_mobile": "bottom",
        "table_columns": 4,
        "table_mt_mobile": 32,
        "table_column_active": "1",
        "table_radius": 8,
        "table_border_thickness": 3,
        "table_heading_image_size": 100,
        "table_heading_image_size_mobile": 80,
        "first_heading": "OS",
        "second_heading": "Liquid IV",
        "third_heading": "LMNT",
        "four_heading": "Nuun",
        "table_heading_custom": false,
        "table_heading_font": "assistant_n4",
        "table_heading_size": 20,
        "table_heading_size_mobile": 16,
        "table_heading_height": 100,
        "table_heading_align": "center",
        "table_heading_padding_horizontal": 18,
        "table_heading_padding_horizontal_mobile": 18,
        "table_heading_padding_vertical": 18,
        "table_heading_padding_vertical_mobile": 18,
        "table_row_padding_horizontal": 14,
        "table_row_padding_horizontal_mobile": 14,
        "table_row_padding_vertical": 16,
        "table_row_padding_vertical_mobile": 16,
        "table_row_heading_width_mobile": 66,
        "table_row_heading_custom": false,
        "table_row_heading_font": "assistant_n4",
        "table_row_heading_size": 16,
        "table_row_heading_size_mobile": 14,
        "table_row_heading_height": 130,
        "table_row_heading_align": "left",
        "table_row_text_custom": false,
        "table_row_text_font": "assistant_n4",
        "table_row_text_size": 20,
        "table_row_text_size_mobile": 16,
        "table_row_text_height": 130,
        "table_row_text_align": "left",
        "table_row_image_size": 24,
        "table_row_image_size_mobile": 24,
        "heading_color": "#2a2a2a",
        "text_color": "#2a2a2a",
        "icon_color": "#2a2a2a",
        "list_text_color": "#2a2a2a",
        "button_color": "#e7e8e5",
        "button_hover_color": "#ffd0d0",
        "button_bg_color": "#2a2a2a",
        "button_bg_hover_color": "#2a2a2a",
        "button_border_color": "#2a2a2a",
        "button_border_hover_color": "#2a2a2a",
        "table_border_color": "#2a2a2a",
        "table_active_border_color": "#e7e8e5",
        "table_heading_color": "#2a2a2a",
        "table_heading_active_color": "#e7e8e5",
        "table_heading_bg_active_color": "#2a2a2a",
        "table_column_active_bg_color": "#2a2a2a",
        "table_row_heading_color": "#2a2a2a",
        "table_row_text_color": "#2a2a2a",
        "table_row_active_text_color": "#e7e8e5",
        "background_color": "#e7e8e5",
        "background_gradient": "",
        "border_color": "#2a2a2a",
        "margin_top": 0,
        "margin_bottom": 0,
        "padding_top": 72,
        "padding_bottom": 72,
        "padding_horizontal": 5,
        "padding_horizontal_mobile": 1.5,
        "full_width": false,
        "content_width": 130,
        "border_thickness": 0,
        "lazy": false
      }
    },
    "featured-product": {
      "type": "featured-product",
      "blocks": {
        "judge_me_reviews_preview_badge_homepage_Rinpdp": {
          "type": "shopify://apps/judge-me-reviews/blocks/preview_badge_homepage/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {
            "product": "os-electrolyte-stick-packs"
          }
        },
        "title": {
          "type": "title",
          "settings": {
            "heading_tag": "h2"
          }
        },
        "rating": {
          "type": "rating",
          "disabled": true,
          "settings": {
            "show_empty": true,
            "rating_text": "Rated 4.9 | 27,956 Reviews",
            "rating_text_font_family": "FormaDJRMicro",
            "rating_text_size": "text-base",
            "rating_text_weight": 400
          }
        },
        "price": {
          "type": "price",
          "settings": {
            "price_font_family": "FormaDJRBanner",
            "price_size": "text-xl",
            "price_layout": "inline",
            "show_taxes_notice": false,
            "secondary_text": "30 Servings ($1.67 / serving)",
            "secondary_text_size": "text-sm",
            "secondary_text_alignment": "left"
          }
        },
        "benefits_fQ63tp": {
          "type": "benefits",
          "settings": {
            "title": "",
            "text_1": "improved focus",
            "text_2": "reduced anxiety",
            "text_3": "max hydration",
            "text_4": "anti-stress"
          }
        },
        "offer_grid_KAzayM": {
          "type": "offer_grid",
          "settings": {
            "desktop_layout": "row",
            "mobile_layout": "stacked",
            "title_font_family": "body",
            "title_font_weight": 400,
            "title_font_size": "text-base",
            "content_font_family": "body",
            "content_font_weight": 400,
            "content_font_size": "text-sm",
            "background_color": "#dddddd",
            "title_color": "#0d0d0d",
            "content_color": "#0d0d0d",
            "border_radius": "rounded-lg",
            "title_1": "300mg",
            "content_1": "<p>MAGNESIUM</p>",
            "title_2": "500mg",
            "content_2": "<p>SODIUM</p>",
            "title_3": "250mg",
            "content_3": "<p>POTASSIUM</p>"
          }
        },
        "badges": {
          "type": "badges",
          "settings": {}
        },
        "collapsible_text_6bqhPg": {
          "type": "collapsible_text",
          "settings": {
            "title": "THREE FORMS OF BIOACTIVE MAGNESIUM",
            "content": "<p>Unlike other brands that use magnesium that use Citrate, which helps your bowel movements, our blend is different. It's composed of Glycinate, Taurate, and Malate which are proven to: </p><ul><li>Increase Cellular Efficiency</li><li>Induce a State of Clarity</li><li>Reduces Stress</li><li>Improve Energy Levels </li><li>Improve Sleep</li></ul>",
            "page": ""
          }
        },
        "collapsible_text_yfqYay": {
          "type": "collapsible_text",
          "settings": {
            "title": "OVER 84 IONIC TRACE MINERALS",
            "content": "<p>OS Lytes uses whole-food sourced ingredients in their natural forms. This includes trace minerals including Boron, Selenium, Manganese, Zinc, Phophorus, and more</p><p></p>",
            "page": ""
          }
        },
        "collapsible_text_WYdJxU": {
          "type": "collapsible_text",
          "settings": {
            "title": "NATURALLY SELECTED",
            "content": "<ul><li>No Added Sugars</li><li>No Artificial Food Dyes </li><li>No Artificial Preservatives</li><li>No Fillers</li><li>No Artificial Sweeteners</li></ul>",
            "page": ""
          }
        },
        "description": {
          "type": "description",
          "disabled": true,
          "settings": {
            "collapse_content": false
          }
        },
        "quantity-selector": {
          "type": "quantity_selector",
          "disabled": true,
          "settings": {}
        },
        "buy-buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_payment_button": false,
            "show_gift_card_recipient": true,
            "atc_button_background": "#47de47",
            "atc_button_text_color": "#0d0d0d",
            "payment_button_background": "",
            "payment_button_text_color": ""
          }
        }
      },
      "block_order": [
        "judge_me_reviews_preview_badge_homepage_Rinpdp",
        "title",
        "rating",
        "price",
        "benefits_fQ63tp",
        "offer_grid_KAzayM",
        "badges",
        "collapsible_text_6bqhPg",
        "collapsible_text_yfqYay",
        "collapsible_text_WYdJxU",
        "description",
        "quantity-selector",
        "buy-buttons"
      ],
      "disabled": true,
      "custom_css": [
        ".accordion__toggle.bold {font-weight: normal;}",
        " /* fkDevS */.benefits-list li {background: black; color: white; border-radius: 100px; display: flex; justify-content: center; align-items: center; height: 55px;}",
        ".benefits-list li img {display: none;}",
        ".benefits-list li span {text-align: center;}",
        ".offer-grid__item {border: 2px solid; border-radius: 0.5rem;}",
        " /* fkDevE */"
      ],
      "settings": {
        "full_width": true,
        "product": "os-electrolyte-stick-packs",
        "desktop_media_width": 50,
        "desktop_media_layout": "carousel_thumbnails_bottom",
        "mobile_media_size": "expanded",
        "mobile_carousel_control": "floating_dots",
        "enable_video_autoplay": true,
        "enable_video_looping": false,
        "enable_image_zoom": false,
        "max_image_zoom_level": 2,
        "background": "#e7e8e5",
        "background_gradient": "",
        "text_color": "rgba(0,0,0,0)",
        "input_background": "#1a1a1a",
        "input_text_color": "#ffffff",
        "section_spacing_block_start": 0,
        "section_spacing_block_start_mobile": 0
      }
    },
    "ugc_scroller_TJGEPb": {
      "type": "ugc-scroller",
      "blocks": {
        "testimonial_KCM8zj": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/guy_running.jpg",
            "title": "",
            "text": "",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_PmUQXp": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/dialed_in_1038d359-602a-4512-a9ae-e86a6d2919ca.jpg",
            "title": "",
            "text": "",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_QtJAen": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/think_bigger.png",
            "title": "",
            "text": "",
            "name": "",
            "link_url": ""
          }
        },
        "testimonial_NWifcQ": {
          "type": "testimonial",
          "settings": {
            "image": "shopify://shop_images/jobs.jpg",
            "title": "",
            "text": "",
            "name": "",
            "link_url": ""
          }
        }
      },
      "block_order": [
        "testimonial_KCM8zj",
        "testimonial_PmUQXp",
        "testimonial_QtJAen",
        "testimonial_NWifcQ"
      ],
      "custom_css": [
        ".luna-swiper-testimonials {z-index: 4;}"
      ],
      "settings": {
        "title": "<p>Join the cult</p>",
        "subtitle": "",
        "color_text": "#ffffff",
        "color_bg": "#e7e8e5",
        "container": "full",
        "color_bg_block": "#0d0d0d",
        "color_text_block": "#e7e8e5",
        "color_border_block": "#000000",
        "border_width_block": 0,
        "border-media": false,
        "block_padding": true,
        "show_stars": false,
        "shape": "rounded",
        "text_align": "center",
        "media_size": "9/16",
        "s_title_font_desktop": 50,
        "b_title_font_desktop": 22,
        "b_text_font_desktop": 22,
        "b_name_font_desktop": 22,
        "s_title_font_mobile": 22,
        "b_title_font_mobile": 22,
        "b_text_font_mobile": 22,
        "b_name_font_mobile": 22,
        "block_count": 3,
        "navigation": "none",
        "rotate": true,
        "speed-slider": 4,
        "padding-lr": 0,
        "padding-lr-m": 0,
        "slide-space": 20,
        "padding_top": 30,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "faq_9CFhia": {
      "type": "faq",
      "blocks": {
        "item_a9GK4W": {
          "type": "item",
          "settings": {
            "title": "WHY THREE FORMS OF MAGNESIUM?",
            "content": "<p>Each has distinct biochemical properties: </p><p><strong>Magnesium Taurate: </strong>for <em>focus</em> and neurological function</p><p><strong>Magnesium Glycinate</strong>: for sleep quality and <em>calming</em> effects on the nervous system</p><p><strong>Magnesium Malate: </strong>for ATP production and <em>energy</em> metabolism</p><p></p>"
          }
        },
        "item_93EVNt": {
          "type": "item",
          "settings": {
            "title": "HOW DO I VERIFY YOUR TEST RESULTS?",
            "content": "<p>By clicking <em>here.</em> </p>"
          }
        },
        "item_MNXpd4": {
          "type": "item",
          "settings": {
            "title": "WHAT MAKES YOUR ELECTROLYTES DIFFERENT?",
            "content": "<ol><li>We deliver a substantial <em>300mg of magnesium</em> per serving using three highly bioavailable forms. While others may sneak in low doses of magnesium, ours has <em>enough for you to feel.</em>  </li><li>We publish <em>third-party test results</em> for every batch, <em>verifying active ingredients</em> and <em>absense of heavy metals</em>.</li><li> We've <em>eliminated unnecessary additives</em> common in other products - 0 sugar, 0 artificial sweeteners, 0 fillers, 0 preservatives, 0 artificial colors.</li><li>Our electrolyte profile reflects <em>optimal mineral ratios</em> for cellular function rather than simply following industry norms.  </li><li>Our flavors are <em>downright addicting</em>. </li></ol>"
          }
        },
        "item_JQWQ3K": {
          "type": "item",
          "settings": {
            "title": "WHY IS THERE 10 CALORIES IF THERE IS NO SUGAR?",
            "content": "<p>Magnesium taurate contains taurate (an amino acid), and magnesium glycinate contains glycine (another amino acid). Both provide approximately 4 calories per gram, just like protein. </p><p>Additionally, citric acid is used as a flavoring agent. It contains 3 calories per gram and has a low glycemic index.<br/><br/>Together, these will have a negligible impact on blood sugar making OS Electrolyte Pods ketogenic, low-carb, and diabetic friendly. </p>"
          }
        },
        "item_GeeC4b": {
          "type": "item",
          "settings": {
            "title": "HOW MANY CAN I HAVE PER DAY?",
            "content": "<p>Most users function optimally with 1-2 sticks daily. Each dosage loads you up with 300mg of magnesium, so we recommend staying under 3 sticks per day. </p>"
          }
        },
        "item_6GfVhV": {
          "type": "item",
          "settings": {
            "title": "HOW LONG WILL IT TAKE TO GET MY ORDER?",
            "content": "<p>It depends on where you are. Orders processed here will take 2-3 business days to arrive in lower 48 states. Overseas deliveries can take anywhere from 7-10 days. Delivery details will be provided in your confirmation email.</p>"
          }
        }
      },
      "block_order": [
        "item_a9GK4W",
        "item_93EVNt",
        "item_MNXpd4",
        "item_JQWQ3K",
        "item_GeeC4b",
        "item_6GfVhV"
      ],
      "custom_css": [
        "/* fkDevS */.accordion-box {background: transparent;}",
        ".accordion {border-bottom: 2px solid; padding-top: 10px; padding-bottom: 10px;}",
        ".accordion .accordion__toggle .circle-chevron {display: none;}",
        ".accordion__toggle:first-child {font-size: 1.5rem;}",
        "@media (max-width: 500px) {.accordion__toggle:first-child {font-size: 1.2rem; }}",
        " /* fkDevE */"
      ],
      "settings": {
        "full_width": true,
        "subheading": "",
        "title": "FAQs",
        "content": "",
        "team_avatar_width": 160,
        "support_hours": "Our customer support is available Monday to Friday: 8am-8:30pm.",
        "answer_time": "Average answer time: 0.25hrs.",
        "button_text": "",
        "button_url": "",
        "text_position": "center",
        "background": "",
        "background_gradient": "",
        "text_color": "rgba(0,0,0,0)",
        "heading_color": "",
        "heading_gradient": "",
        "button_background": "",
        "button_text_color": "",
        "accordion_background": "",
        "accordion_text_color": ""
      }
    },
    "1743968641c1eea6b7": {
      "type": "apps",
      "settings": {
        "full_width": true,
        "remove_vertical_spacing": false,
        "remove_horizontal_spacing": false,
        "background": "",
        "background_gradient": "",
        "text_color": ""
      }
    }
  },
  "order": [
    "images_with_text_scrolling_yFGt6Y",
    "scrolling_text_e3NjJK",
    "scroll_banner_xxdKei",
    "rich_text_Bx69PA",
    "impact_text_x7kAdD",
    "impact_text_Xa3XqW",
    "ss_comparison_table_12_NhfG6r",
    "slider_6tbCGV",
    "ss_steps_5_AYgqCp",
    "flavor_section_dHQRac",
    "ss_comparison_table_6_zetXqi",
    "featured-product",
    "ugc_scroller_TJGEPb",
    "faq_9CFhia",
    "1743968641c1eea6b7"
  ]
}
